package com.logictrue.word.service;

import com.logictrue.word.dto.JsonTableExportRequest;
import com.logictrue.word.dto.TableExportRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.openxmlformats.schemas.officeDocument.x2006.math.CTOMath;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Word导出服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WordExportService {

    // 像素到磅的转换比例（1磅 = 1.33像素）
    private static final double PIXEL_TO_POINT = 0.75;
    // 磅到Twips的转换比例（1磅 = 20 Twips）
    private static final int POINT_TO_TWIPS = 20;

    /**
     * 导出表格到Word文档
     */
    public byte[] exportTableToWord(TableExportRequest request) throws IOException {
        log.info("开始导出Word文档，表格标题: {}", request.getTitle());

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页面方向
            setPageOrientation(document, request.getPageOrientation());

            // 添加标题
            if (request.getTitle() != null && !request.getTitle().trim().isEmpty()) {
                XWPFParagraph titleParagraph = document.createParagraph();
                titleParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun titleRun = titleParagraph.createRun();
                titleRun.setText(request.getTitle());
                titleRun.setBold(true);
                titleRun.setFontSize(16);
                titleRun.setFontFamily("宋体");
            }

            // 创建表格
            createTable(document, request);

            document.write(out);
            byte[] result = out.toByteArray();

            log.info("Word文档导出完成，文件大小: {} bytes", result.length);
            return result;

        } catch (Exception e) {
            log.error("导出Word文档失败", e);
            throw new IOException("导出Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建表格
     */
    private void createTable(XWPFDocument document, TableExportRequest request) {
        TableExportRequest.TableData tableData = request.getTableData();
        if (tableData == null) {
            log.warn("表格数据为空，跳过创建表格");
            return;
        }

        // 调试：打印接收到的表头数据
        if (tableData.getHeaders() != null) {
            log.info("接收到表头数据，共{}行", tableData.getHeaders().size());
            for (int i = 0; i < tableData.getHeaders().size(); i++) {
                List<TableExportRequest.HeaderCell> row = tableData.getHeaders().get(i);
                log.info("第{}行表头，共{}列", i + 1, row.size());
                for (int j = 0; j < row.size(); j++) {
                    TableExportRequest.HeaderCell cell = row.get(j);
                    if (cell.getContent() != null && !cell.getContent().trim().isEmpty()) {
                        log.info("  列{}: content='{}', rowspan={}, colspan={}",
                                j, cell.getContent(), cell.getRowspan(), cell.getColspan());
                    }
                }
            }
        }

        // 计算总行数
        int headerRows = tableData.getHeaders() != null ? tableData.getHeaders().size() : 0;
        int dataRows = tableData.getDataRows() != null ? tableData.getDataRows().size() : 0;
        int totalRows = headerRows + dataRows;

        if (totalRows == 0) {
            log.warn("表格行数为0，跳过创建表格");
            return;
        }

        // 计算列数 - 使用固定的8列（根据前端表格结构）
        int cols = 8;

        log.info("创建表格: {}行 x {}列", totalRows, cols);

        // 创建表格
        XWPFTable table = document.createTable(totalRows, cols);

        // 设置表格宽度和列宽
        setTableWidthAndColumns(table, tableData, request.getTableWidth());

        // 设置表格样式
        table.getCTTbl().getTblPr().unsetTblBorders();

        int currentRow = 0;

        // 处理表头 - 支持单元格合并
        if (headerRows > 0) {
            currentRow = processHeadersWithMerge(table, tableData.getHeaders());
        }

        // 处理数据行
        if (dataRows > 0) {
            for (List<TableExportRequest.DataCell> dataRow : tableData.getDataRows()) {
                if (currentRow < table.getRows().size()) {
                    XWPFTableRow row = table.getRow(currentRow);
                    processDataRow(row, dataRow);
                } else {
                    // 如果行数不够，创建新行
                    XWPFTableRow newRow = table.createRow();
                    processDataRow(newRow, dataRow);
                }
                currentRow++;
            }
        }

        // 设置表格边框
        setTableBorders(table);

        log.info("表格创建完成");
    }

    /**
     * 处理支持单元格合并的表头
     */
    private int processHeadersWithMerge(XWPFTable table, List<List<TableExportRequest.HeaderCell>> headers) {
        if (headers == null || headers.isEmpty()) {
            return 0;
        }

        log.info("开始处理表头，共{}行", headers.size());

        int processedRows = 0;

        for (int rowIndex = 0; rowIndex < headers.size(); rowIndex++) {
            List<TableExportRequest.HeaderCell> headerRow = headers.get(rowIndex);
            log.info("处理第{}行表头，共{}列", rowIndex + 1, headerRow.size());

            if (rowIndex < table.getRows().size()) {
                XWPFTableRow row = table.getRow(rowIndex);
                processHeaderRowWithMerge(row, headerRow, rowIndex);
                processedRows++;
            }
        }

        log.info("表头处理完成，共处理{}行", processedRows);
        return processedRows;
    }

    /**
     * 处理单行表头，支持单元格合并
     */
    private void processHeaderRowWithMerge(XWPFTableRow row, List<TableExportRequest.HeaderCell> headerCells, int rowIndex) {
        for (int i = 0; i < headerCells.size() && i < row.getTableCells().size(); i++) {
            TableExportRequest.HeaderCell headerCell = headerCells.get(i);
            XWPFTableCell cell = row.getCell(i);

            // 处理有内容的单元格
            if (headerCell.getContent() != null && !headerCell.getContent().trim().isEmpty()) {
                // 添加调试日志
                log.debug("处理表头单元格: content='{}', isVertical={}", headerCell.getContent(), headerCell.getIsVertical());

                // 设置单元格内容（支持纵向显示）
                setHeaderCellContent(cell, headerCell.getContent(), headerCell.getIsVertical());

                // 设置表头单元格尺寸（包含宽度，用于合并单元格）
                setHeaderCellSize(cell, headerCell.getWidth(), headerCell.getHeight());

                // 处理列合并 (colspan)
                if (headerCell.getColspan() != null && headerCell.getColspan() > 1) {
                    mergeHorizontalCells(row, i, headerCell.getColspan());
                    log.debug("合并列: 起始列={}, 合并数={}", i, headerCell.getColspan());
                }

                // 处理行合并 (rowspan) - 需要在表格创建完成后处理
                if (headerCell.getRowspan() != null && headerCell.getRowspan() > 1) {
                    // 标记需要行合并的单元格，稍后处理
                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr == null) {
                        tcPr = cell.getCTTc().addNewTcPr();
                    }
                    tcPr.addNewVMerge().setVal(STMerge.RESTART);
                    markVerticalMergeCells(row.getTable(), rowIndex, i, headerCell.getRowspan());
                    log.debug("标记行合并: 行={}, 列={}, 合并数={}", rowIndex, i, headerCell.getRowspan());
                }
            } else {
                // 处理空的占位符单元格 - 清空内容但保持单元格结构
                cell.removeParagraph(0);
                cell.addParagraph();
                log.debug("处理占位符单元格: 行={}, 列={}", rowIndex, i);
            }
        }
    }

    /**
     * 合并水平单元格（改进版本 - 确保被合并单元格内容清空）
     */
    private void mergeHorizontalCells(XWPFTableRow row, int startCol, int colspan) {
        try {
            log.debug("开始水平合并: 行{}, 起始列{}, 跨度{}", row.getTable().getRows().indexOf(row), startCol, colspan);

            for (int i = 0; i < colspan; i++) {
                if (startCol + i < row.getTableCells().size()) {
                    XWPFTableCell cell = row.getCell(startCol + i);
                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr == null) {
                        tcPr = cell.getCTTc().addNewTcPr();
                    }

                    CTHMerge hMerge = tcPr.getHMerge();
                    if (hMerge == null) {
                        hMerge = tcPr.addNewHMerge();
                    }

                    if (i == 0) {
                        // 主单元格：设置为RESTART
                        hMerge.setVal(STMerge.RESTART);
                        log.debug("主单元格[{}] 设置为RESTART", startCol + i);
                    } else {
                        // 被合并单元格：设置为CONTINUE并清空内容
                        hMerge.setVal(STMerge.CONTINUE);
                        clearCellContentForMerge(cell);
                        log.debug("被合并单元格[{}] 设置为CONTINUE并清空内容", startCol + i);
                    }
                }
            }

            log.debug("水平合并完成: 起始列{}, 跨度{}", startCol, colspan);
        } catch (Exception e) {
            log.warn("水平合并单元格失败: {}", e.getMessage());
        }
    }

    /**
     * 标记垂直合并单元格
     */
    private void markVerticalMergeCells(XWPFTable table, int startRow, int col, int rowspan) {
        try {
            for (int i = 1; i < rowspan; i++) {
                int targetRow = startRow + i;
                if (targetRow < table.getRows().size()) {
                    XWPFTableRow row = table.getRow(targetRow);
                    if (col < row.getTableCells().size()) {
                        XWPFTableCell cell = row.getCell(col);
                        CTTcPr tcPr = cell.getCTTc().getTcPr();
                        if (tcPr == null) {
                            tcPr = cell.getCTTc().addNewTcPr();
                        }
                        tcPr.addNewVMerge().setVal(STMerge.CONTINUE);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("垂直合并单元格失败: {}", e.getMessage());
        }
    }

    /**
     * 设置表头单元格内容（支持纵向显示）
     */
    private void setHeaderCellContent(XWPFTableCell cell, String content, Boolean isVertical) {
        // 清除现有内容
        cell.removeParagraph(0);

        // 创建新段落
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 处理纵向显示
        if (Boolean.TRUE.equals(isVertical)) {
            // 纵向显示：每个字符单独一行
            String cleanContent = cleanHtmlTags(content);
            char[] chars = cleanContent.toCharArray();

            for (int i = 0; i < chars.length; i++) {
                XWPFRun run = paragraph.createRun();
                run.setText(String.valueOf(chars[i]));
                run.setBold(true);
                run.setFontFamily("宋体");
                run.setFontSize(12);

                // 除了最后一个字符，都添加换行
                if (i < chars.length - 1) {
                    run.addBreak();
                }
            }

            log.info("设置纵向文字: '{}' -> '{}' ({}个字符，每字符一行)", content, cleanContent, chars.length);
        } else {
            // 横向显示：处理<br>标签为换行
            processTextWithBreaks(paragraph, content);
            log.debug("设置横向文字: '{}'", content);
        }

        // 设置单元格样式
        cell.setColor("F2F2F2"); // 浅灰色背景
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 处理包含<br>标签的文本，转换为Word换行
     */
    private void processTextWithBreaks(XWPFParagraph paragraph, String content) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }

        // 处理各种<br>标签格式
        String[] parts = content.split("(?i)<br\\s*/?>");

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i].trim();

            if (!part.isEmpty()) {
                XWPFRun run = paragraph.createRun();
                run.setText(part);
                run.setBold(true);
                run.setFontFamily("宋体");
                run.setFontSize(12);
            }

            // 除了最后一部分，都添加换行
            if (i < parts.length - 1) {
                XWPFRun breakRun = paragraph.createRun();
                breakRun.addBreak();
            }
        }

        log.debug("处理<br>标签: '{}' -> {}个文本段", content, parts.length);
    }

    /**
     * 清理HTML标签（用于纵向显示）
     */
    private String cleanHtmlTags(String text) {
        if (text == null) {
            return null;
        }

        // 移除所有HTML标签
        String cleaned = text.replaceAll("(?i)<br\\s*/?>", "")
                .replaceAll("<[^>]+>", "")
                .trim();

        log.debug("清理HTML标签: '{}' -> '{}'", text, cleaned);
        return cleaned;
    }


    /**
     * 处理数据行
     */
    private void processDataRow(XWPFTableRow row, List<TableExportRequest.DataCell> dataCells) {
        for (int i = 0; i < dataCells.size() && i < row.getTableCells().size(); i++) {
            TableExportRequest.DataCell dataCell = dataCells.get(i);
            XWPFTableCell cell = row.getCell(i);

            // 设置单元格内容
            cell.removeParagraph(0); // 移除默认段落
            XWPFParagraph paragraph = cell.addParagraph();
            paragraph.setAlignment(ParagraphAlignment.CENTER);

            // 处理单元格内容
            log.debug("处理数据单元格 - hasMath: {}, hasMultipleContent: {}, content: {}",
                    dataCell.getHasMath(), dataCell.getHasMultipleContent(), dataCell.getContent());

            if (Boolean.TRUE.equals(dataCell.getHasMath())) {
                if (Boolean.TRUE.equals(dataCell.getHasMultipleContent()) && dataCell.getMathMLMap() != null) {
                    // 处理混合内容（公式+文本）
                    log.info("处理混合内容，mathMLMap大小: {}", dataCell.getMathMLMap().size());
                    insertMixedContent(paragraph, dataCell.getContent(), dataCell.getMathMLMap());
                } else if (dataCell.getMathML() != null) {
                    // 处理纯公式内容
                    log.info("处理纯公式内容");
                    insertMathFormula(paragraph, dataCell.getMathML());
                } else {
                    // 处理普通文本内容（兜底处理）
                    log.info("处理普通文本内容（兜底）");
                    String content = dataCell.getContent() != null ? dataCell.getContent() : "";
                    insertTextWithLineBreaks(paragraph, content);
                }
            } else {
                // 处理普通文本内容
                log.info("处理普通文本内容");
                String content = dataCell.getContent() != null ? dataCell.getContent() : "";
                insertTextWithLineBreaks(paragraph, content);
            }

            // 设置单元格样式
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

            // 设置单元格尺寸
            setCellSize(cell, dataCell.getWidth(), dataCell.getHeight());
        }
    }


    /**
     * 设置表格宽度和列宽（原有方法，保持兼容性）
     */
    private void setTableWidthAndColumns(XWPFTable table, TableExportRequest.TableData tableData, Integer tableWidthPx) {
        try {
            // 收集所有列的宽度信息（逻辑列，不是物理列）
            int[] columnWidths = new int[8]; // 固定8列
            boolean hasWidthInfo = false;

            // 从数据行获取列宽信息（数据行是最准确的，因为它们是1:1对应的）
            if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
                List<TableExportRequest.DataCell> firstDataRow = tableData.getDataRows().get(0);
                for (int i = 0; i < Math.min(firstDataRow.size(), columnWidths.length); i++) {
                    TableExportRequest.DataCell dataCell = firstDataRow.get(i);
                    if (dataCell.getWidth() != null && dataCell.getWidth() > 0) {
                        columnWidths[i] = dataCell.getWidth();
                        hasWidthInfo = true;
                    }
                }
                log.info("从数据行获取列宽: {}", java.util.Arrays.toString(columnWidths));
            }

            // 如果数据行没有宽度信息，尝试从表头获取
            if (!hasWidthInfo && tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
                // 使用默认宽度
                columnWidths = new int[]{150, 200, 150, 80, 80, 120, 120, 120};
                hasWidthInfo = true;
                log.info("使用默认列宽: {}", java.util.Arrays.toString(columnWidths));
            }

            if (hasWidthInfo) {
                // 计算总宽度
                int totalWidth = 0;
                for (int width : columnWidths) {
                    totalWidth += width;
                }

                // 设置表格总宽度
                if (totalWidth > 0) {
                    int totalWidthTwips = (int) (totalWidth * 15); // 像素转Twips
                    table.setWidth(String.valueOf(totalWidthTwips));
                    log.info("设置表格总宽度: {}px ({}twips)", totalWidth, totalWidthTwips);
                } else {
                    table.setWidth("100%");
                }

                // 只为数据行设置列宽（表头的合并单元格会自动处理）
                setDataRowsColumnWidth(table, columnWidths);

                log.info("设置列宽完成: {}", java.util.Arrays.toString(columnWidths));
            } else {
                // 没有宽度信息，使用默认设置
                table.setWidth("100%");
                log.info("未找到列宽信息，使用默认宽度设置");
            }

        } catch (Exception e) {
            log.warn("设置表格宽度失败: {}", e.getMessage(), e);
            table.setWidth("100%");
        }
    }


    /**
     * 为数据行设置列宽（保持原有方法兼容性）
     */
    private void setDataRowsColumnWidth(XWPFTable table, int[] columnWidths) {
        try {
            // 找到数据行的起始位置（跳过表头行）
            int headerRowCount = 2; // 表头有2行

            for (int i = headerRowCount; i < table.getRows().size(); i++) {
                XWPFTableRow row = table.getRow(i);
                List<XWPFTableCell> cells = row.getTableCells();

                // 为每个数据单元格设置宽度
                for (int j = 0; j < Math.min(cells.size(), columnWidths.length); j++) {
                    if (columnWidths[j] > 0) {
                        XWPFTableCell cell = cells.get(j);
                        int widthTwips = (int) (columnWidths[j] * 15);
                        cell.setWidth(String.valueOf(widthTwips));
                        log.debug("设置数据行[{}]列[{}]宽度: {}px ({}twips)", i, j, columnWidths[j], widthTwips);
                    }
                }
            }

            log.info("数据行列宽设置完成");
        } catch (Exception e) {
            log.warn("设置数据行列宽失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 设置单元格尺寸
     */
    private void setCellSize(XWPFTableCell cell, Integer widthPx, Integer heightPx) {
        // 只设置高度，不设置宽度（宽度由统一的列宽设置处理）
        if (heightPx != null && heightPx > 0) {
            // 设置行高（POI中行高设置相对复杂）
            try {
                XWPFTableRow row = cell.getTableRow();
                CTTrPr trPr = row.getCtRow().getTrPr();
                if (trPr == null) {
                    trPr = row.getCtRow().addNewTrPr();
                }

                // 设置行高
                CTHeight rowHeight = null;
                if (trPr.getTrHeightList().isEmpty()) {
                    rowHeight = trPr.addNewTrHeight();
                } else {
                    rowHeight = trPr.getTrHeightList().get(0);
                }

                // 将像素转换为Twips
                int heightTwips = (int) (heightPx * 15);
                rowHeight.setVal(BigInteger.valueOf(heightTwips));
                rowHeight.setHRule(STHeightRule.AT_LEAST);
                log.debug("设置单元格高度: {}px ({}twips)", heightPx, heightTwips);
            } catch (Exception e) {
                log.warn("设置单元格高度失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 设置表头单元格尺寸（包含宽度设置，用于合并单元格）
     */
    private void setHeaderCellSize(XWPFTableCell cell, Integer widthPx, Integer heightPx) {
        if (widthPx != null && widthPx > 0) {
            // 将像素转换为Twips (1像素 ≈ 15 Twips)
            int widthTwips = (int) (widthPx * 15);

            // 使用与SimpleWordService相同的可靠方式设置单元格宽度
            try {
                CTTcPr tcPr = cell.getCTTc().getTcPr();
                if (tcPr == null) {
                    tcPr = cell.getCTTc().addNewTcPr();
                }

                CTTblWidth cellWidth = tcPr.getTcW();
                if (cellWidth == null) {
                    cellWidth = tcPr.addNewTcW();
                }

                cellWidth.setType(STTblWidth.DXA);
                cellWidth.setW(BigInteger.valueOf(widthTwips));

                log.debug("设置表头单元格宽度: {}px ({}twips)", widthPx, widthTwips);
            } catch (Exception e) {
                log.warn("设置表头单元格宽度失败: {}", e.getMessage());
                // 备选方案：使用简单的setWidth方法
                cell.setWidth(String.valueOf(widthTwips));
            }
        }

        if (heightPx != null && heightPx > 0) {
            // 设置行高
            try {
                XWPFTableRow row = cell.getTableRow();
                CTTrPr trPr = row.getCtRow().getTrPr();
                if (trPr == null) {
                    trPr = row.getCtRow().addNewTrPr();
                }

                // 设置行高
                CTHeight rowHeight = null;
                if (trPr.getTrHeightList().isEmpty()) {
                    rowHeight = trPr.addNewTrHeight();
                } else {
                    rowHeight = trPr.getTrHeightList().get(0);
                }

                // 将像素转换为Twips
                int heightTwips = (int) (heightPx * 15);
                rowHeight.setVal(BigInteger.valueOf(heightTwips));
                rowHeight.setHRule(STHeightRule.AT_LEAST);
                log.debug("设置表头单元格高度: {}px ({}twips)", heightPx, heightTwips);
            } catch (Exception e) {
                log.warn("设置表头单元格高度失败: {}", e.getMessage());
            }
        }
    }


    /**
     * 插入数学公式到段落
     */
    private void insertMathFormula(XWPFParagraph paragraph, String mathML) {
        try {
            log.info("插入数学公式，MathML: {}", mathML);

            // 将MathML转换为OMML
            CTOMath ctOMath = MathMLToOMMLService.convertOMML(mathML);

            CTP ctp = paragraph.getCTP();
            ctp.setOMathArray(new CTOMath[]{ctOMath});

        } catch (Exception e) {
            log.error("插入数学公式失败: {}", e.getMessage(), e);
            // 插入错误提示文本
            XWPFRun run = paragraph.createRun();
            run.setText("[公式错误]");
            run.setFontFamily("宋体");
            run.setFontSize(11);
        }
    }

    /**
     * 插入混合内容（公式+文本）到段落
     */
    private void insertMixedContent(XWPFParagraph paragraph, String content, Map<String, String> mathMLMap) {
        try {
            log.info("插入混合内容，内容: {}, 公式映射: {}", content, mathMLMap);

            if (content == null || content.isEmpty()) {
                return;
            }

            // 创建一个列表来存储内容片段和它们的类型
            java.util.List<ContentPart> parts = new java.util.ArrayList<>();

            // 使用正则表达式找到所有占位符
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(__MATH_FORMULA_\\d+__)");
            java.util.regex.Matcher matcher = pattern.matcher(content);

            int lastEnd = 0;

            while (matcher.find()) {
                // 添加占位符前的文本
                if (matcher.start() > lastEnd) {
                    String textPart = content.substring(lastEnd, matcher.start());
                    if (!textPart.isEmpty()) {
                        parts.add(new ContentPart(textPart, false));
                    }
                }

                // 添加占位符（公式）
                String placeholder = matcher.group(1);
                parts.add(new ContentPart(placeholder, true));

                lastEnd = matcher.end();
            }

            // 添加最后一个占位符后的文本
            if (lastEnd < content.length()) {
                String remainingText = content.substring(lastEnd);
                if (!remainingText.isEmpty()) {
                    parts.add(new ContentPart(remainingText, false));
                }
            }

            // 如果没有找到占位符，整个内容都是文本
            if (parts.isEmpty()) {
                parts.add(new ContentPart(content, false));
            }

            // 按顺序插入所有部分
            for (ContentPart part : parts) {
                if (part.isFormula) {
                    // 这是公式占位符
                    String mathML = mathMLMap.get(part.content);
                    if (mathML != null && !mathML.isEmpty()) {
                        try {
                            // 创建一个新的段落来插入公式
                            insertMathFormulaInline(paragraph, mathML);
                            log.debug("成功插入公式: {}", part.content);
                        } catch (Exception e) {
                            log.error("插入公式失败: {}, 错误: {}", part.content, e.getMessage());
                            XWPFRun errorRun = paragraph.createRun();
                            errorRun.setText("[公式错误:" + part.content + "]");
                            errorRun.setFontFamily("宋体");
                            errorRun.setFontSize(11);
                        }
                    } else {
                        log.warn("未找到占位符对应的MathML: {}", part.content);
                        XWPFRun placeholderRun = paragraph.createRun();
                        placeholderRun.setText("[缺失公式:" + part.content + "]");
                        placeholderRun.setFontFamily("宋体");
                        placeholderRun.setFontSize(11);
                    }
                } else {
                    // 这是普通文本
                    insertTextPartWithLineBreaks(paragraph, part.content);
                    log.debug("插入文本: {}", part.content);
                }
            }

        } catch (Exception e) {
            log.error("插入混合内容失败: {}", e.getMessage(), e);
            // 降级处理：插入原始内容
            String fallbackContent = content != null ? content : "[混合内容错误]";
            insertTextWithLineBreaks(paragraph, fallbackContent);
        }
    }

    /**
     * 内联插入数学公式（用于混合内容）
     */
    private void insertMathFormulaInline(XWPFParagraph paragraph, String mathML) throws Exception {
        // 将MathML转换为OMML
        CTOMath ctOMath = MathMLToOMMLService.convertOMML(mathML);

        paragraph.getCTP().addNewOMath().set(ctOMath);
    }

    /**
     * 内容片段类，用于区分文本和公式
     */
    private static class ContentPart {
        String content;
        boolean isFormula;

        ContentPart(String content, boolean isFormula) {
            this.content = content;
            this.isFormula = isFormula;
        }
    }


    /**
     * 设置文档纸张方向
     */
    private void setPageOrientation(XWPFDocument document, String orientation) {
        try {
            CTDocument1 doc = document.getDocument();
            CTBody body = doc.getBody();

            if (!body.isSetSectPr()) {
                body.addNewSectPr();
            }
            CTSectPr section = body.getSectPr();

            if (!section.isSetPgSz()) {
                section.addNewPgSz();
            }

            CTPageSz pageSize = section.getPgSz();

            // 默认为横向，除非明确指定为纵向
            boolean isLandscape = !"PORTRAIT".equalsIgnoreCase(orientation);

            if (isLandscape) {
                // 横向 (A4纸张: 宽297mm, 高210mm)
                // 1英寸 = 1440 twips, 1mm = 56.7 twips
                pageSize.setW(BigInteger.valueOf(16838)); // 297mm * 56.7 ≈ 16838 twips
                pageSize.setH(BigInteger.valueOf(11906)); // 210mm * 56.7 ≈ 11906 twips
                pageSize.setOrient(STPageOrientation.LANDSCAPE);
                log.info("已设置文档为横向纸张");
            } else {
                // 纵向 (A4纸张: 宽210mm, 高297mm)
                pageSize.setW(BigInteger.valueOf(11906)); // 210mm * 56.7 ≈ 11906 twips
                pageSize.setH(BigInteger.valueOf(16838)); // 297mm * 56.7 ≈ 16838 twips
                pageSize.setOrient(STPageOrientation.PORTRAIT);
                log.info("已设置文档为纵向纸张");
            }
        } catch (Exception e) {
            log.warn("设置纸张方向失败: {}", e.getMessage());
        }
    }

    /**
     * 插入包含换行符的文本到段落中
     *
     * @param paragraph Word段落对象
     * @param text      包含换行符的文本
     */
    private void insertTextWithLineBreaks(XWPFParagraph paragraph, String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        // 按换行符分割文本
        String[] lines = text.split("\n");

        for (int i = 0; i < lines.length; i++) {
            XWPFRun run = paragraph.createRun();
            run.setText(lines[i]);
            run.setFontFamily("宋体");
            run.setFontSize(11);

            // 如果不是最后一行，添加换行符
            if (i < lines.length - 1) {
                run.addBreak();
            }
        }

        log.debug("插入包含换行符的文本，行数: {}", lines.length);
    }

    /**
     * 在混合内容中插入包含换行符的文本片段
     *
     * @param paragraph Word段落对象
     * @param text      包含换行符的文本片段
     */
    private void insertTextPartWithLineBreaks(XWPFParagraph paragraph, String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        // 按换行符分割文本
        String[] lines = text.split("\n");

        for (int i = 0; i < lines.length; i++) {
            XWPFRun run = paragraph.createRun();
            run.setText(lines[i]);
            run.setFontFamily("宋体");
            run.setFontSize(11);

            // 添加换行符
            run.addBreak();
        }
    }

    /**
     * 添加标题
     */
    private void addTitle(XWPFDocument document, String title) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");
        titleParagraph.setSpacingAfter(300);
    }


    /**
     * 设置单元格内容
     */
    private void setCellContentOptimized(XWPFTableCell cell, String content, boolean isHeader, Boolean isVertical) {
        // 清除默认段落
        cell.removeParagraph(0);

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 处理纵向显示
        if (Boolean.TRUE.equals(isVertical)) {
            // 纵向显示：每个字符单独一行
            String cleanContent = cleanHtmlTags(content);
            char[] chars = cleanContent.toCharArray();

            for (int i = 0; i < chars.length; i++) {
                XWPFRun run = paragraph.createRun();
                run.setText(String.valueOf(chars[i]));
                run.setFontFamily("宋体");
                run.setFontSize(isHeader ? 12 : 11);
                if (isHeader) {
                    run.setBold(true);
                }

                // 除了最后一个字符，都添加换行
                if (i < chars.length - 1) {
                    run.addBreak();
                }
            }
        } else {
            // 横向显示：处理换行符
            XWPFRun run = paragraph.createRun();
            run.setText(content);
            run.setFontFamily("宋体");
            run.setFontSize(isHeader ? 12 : 11);
            if (isHeader) {
                run.setBold(true);
            }
        }

        // 设置单元格样式
        if (isHeader) {
            cell.setColor("F2F2F2"); // 浅灰色背景
        }
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 设置单元格宽度
     */
    private void setCellWidthOptimized(XWPFTableCell cell, int widthPx) {
        int widthTwips = widthPx * 15;

        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = cell.getCTTc().addNewTcPr();
        }

        CTTblWidth cellWidth = tcPr.getTcW();
        if (cellWidth == null) {
            cellWidth = tcPr.addNewTcW();
        }

        cellWidth.setType(STTblWidth.DXA);
        cellWidth.setW(BigInteger.valueOf(widthTwips));
    }

    /**
     * 设置行高
     */
    private void setCellHeightOptimized(XWPFTableRow row, int heightPx) {
        try {
            int heightTwips = heightPx * 15;

            CTTrPr trPr = row.getCtRow().getTrPr();
            if (trPr == null) {
                trPr = row.getCtRow().addNewTrPr();
            }

            CTHeight rowHeight = trPr.getTrHeightList().isEmpty() ?
                    trPr.addNewTrHeight() : trPr.getTrHeightList().get(0);

            rowHeight.setVal(BigInteger.valueOf(heightTwips));
            rowHeight.setHRule(STHeightRule.AT_LEAST);

            log.debug("成功设置行高: {}px -> {}twips, 规则: AT_LEAST", heightPx, heightTwips);
        } catch (Exception e) {
            log.error("设置行高失败: {}px, 错误: {}", heightPx, e.getMessage(), e);
        }
    }

    /**
     * 检查是否为合并单元格的主单元格
     */
    private boolean isMergedMainCell(List<TableExportRequest.MergeCell> merges, int rowIndex, int colIndex) {
        if (merges == null || merges.isEmpty()) {
            return false;
        }

        for (TableExportRequest.MergeCell merge : merges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取合并单元格的内容
     */
    private String getMergedCellContent(List<TableExportRequest.MergeCell> merges, int rowIndex, int colIndex) {
        if (merges == null || merges.isEmpty()) {
            return null;
        }

        for (TableExportRequest.MergeCell merge : merges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                return merge.getContent();
            }
        }
        return null;
    }

    /**
     * 应用水平合并（修复版本 - 不使用gridSpan避免列数扩展）
     */
    private void applyHorizontalMerge(XWPFTable table, int row, int startCol, int endCol) {
        XWPFTableRow tableRow = table.getRow(row);
        XWPFTableCell mainCell = tableRow.getCell(startCol);

        int colspan = endCol - startCol + 1;
        log.info("应用水平合并: 行{}, 列{}-{}, 跨度{}列", row, startCol, endCol, colspan);

        // 获取主单元格属性
        CTTcPr tcPr = mainCell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = mainCell.getCTTc().addNewTcPr();
        }

        // 修复：不使用gridSpan，避免列数扩展问题
        // 原因：gridSpan可能导致POI在内部创建额外列，造成表格列数从8列扩展到9列
        log.debug("跳过gridSpan设置，避免列数扩展问题");

        // 设置主单元格的水平合并开始
        CTHMerge mainHMerge = tcPr.getHMerge();
        if (mainHMerge == null) {
            mainHMerge = tcPr.addNewHMerge();
        }
        mainHMerge.setVal(STMerge.RESTART);
        log.debug("主单元格[{},{}] 设置为RESTART", row, startCol);

        // 设置被合并的单元格为合并继续
        for (int col = startCol + 1; col <= endCol; col++) {
            XWPFTableCell cell = tableRow.getCell(col);
            if (cell != null) {
                // 清空被合并单元格的内容（Office兼容性）
                //clearCellContentForMerge(cell);

                CTTcPr cellTcPr = cell.getCTTc().getTcPr();
                if (cellTcPr == null) {
                    cellTcPr = cell.getCTTc().addNewTcPr();
                }

                CTHMerge hMerge = cellTcPr.getHMerge();
                if (hMerge == null) {
                    hMerge = cellTcPr.addNewHMerge();
                }
                hMerge.setVal(STMerge.CONTINUE);
                log.debug("被合并单元格[{},{}] 设置为CONTINUE", row, col);
            }
        }

        log.info("水平合并完成: 行{}, 列{}-{}", row, startCol, endCol);
    }

    /**
     * 清空单元格内容用于合并（Office兼容性）
     */
    private void clearCellContentForMerge(XWPFTableCell cell) {
        try {
            // 移除所有段落
            while (cell.getParagraphs().size() > 0) {
                cell.removeParagraph(0);
            }
            // 添加一个空段落
            XWPFParagraph emptyPara = cell.addParagraph();
            emptyPara.setAlignment(ParagraphAlignment.CENTER);

            // 确保段落没有任何内容
            while (emptyPara.getRuns().size() > 0) {
                emptyPara.removeRun(0);
            }
        } catch (Exception e) {
            log.warn("清空合并单元格内容失败: {}", e.getMessage());
        }
    }

    /**
     * 应用垂直合并
     */
    private void applyVerticalMerge(XWPFTable table, int startRow, int endRow, int col) {
        // 设置主单元格为合并开始
        XWPFTableCell mainCell = table.getRow(startRow).getCell(col);
        CTTcPr mainTcPr = mainCell.getCTTc().getTcPr();
        if (mainTcPr == null) {
            mainTcPr = mainCell.getCTTc().addNewTcPr();
        }
        CTVMerge vMerge = mainTcPr.getVMerge();
        if (vMerge == null) {
            vMerge = mainTcPr.addNewVMerge();
        }
        vMerge.setVal(STMerge.RESTART);

        // 设置其他单元格为合并继续
        for (int row = startRow + 1; row <= endRow; row++) {
            XWPFTableRow tableRow = table.getRow(row);
            if (tableRow != null) {
                XWPFTableCell cell = tableRow.getCell(col);
                if (cell != null) {
                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr == null) {
                        tcPr = cell.getCTTc().addNewTcPr();
                    }
                    CTVMerge cellVMerge = tcPr.getVMerge();
                    if (cellVMerge == null) {
                        cellVMerge = tcPr.addNewVMerge();
                    }
                    cellVMerge.setVal(STMerge.CONTINUE);
                }
            }
        }
    }

    /**
     * 验证合并信息是否有效
     */
    private boolean isValidMergeOptimized(XWPFTable table, TableExportRequest.MergeCell merge, int headerRowCount) {
        if (merge == null) {
            log.warn("合并信息为null");
            return false;
        }

        int tableRows = table.getRows().size();
        int tableCols = table.getRow(0).getTableCells().size();

        int actualStartRow = merge.getStartRow() + headerRowCount;
        int actualEndRow = merge.getEndRow() + headerRowCount;

        // 检查行索引
        if (actualStartRow < 0 || actualStartRow >= tableRows) {
            log.warn("起始行索引超出范围: {} (表格行数: {})", actualStartRow, tableRows);
            return false;
        }
        if (actualEndRow < 0 || actualEndRow >= tableRows) {
            log.warn("结束行索引超出范围: {} (表格行数: {})", actualEndRow, tableRows);
            return false;
        }

        // 检查列索引
        if (merge.getStartCol() < 0 || merge.getStartCol() >= tableCols) {
            log.warn("起始列索引超出范围: {} (表格列数: {})", merge.getStartCol(), tableCols);
            return false;
        }
        if (merge.getEndCol() < 0 || merge.getEndCol() >= tableCols) {
            log.warn("结束列索引超出范围: {} (表格列数: {})", merge.getEndCol(), tableCols);
            return false;
        }

        // 检查合并范围的逻辑性
        if (actualStartRow > actualEndRow) {
            log.warn("起始行大于结束行: {} > {}", actualStartRow, actualEndRow);
            return false;
        }
        if (merge.getStartCol() > merge.getEndCol()) {
            log.warn("起始列大于结束列: {} > {}", merge.getStartCol(), merge.getEndCol());
            return false;
        }

        return true;
    }

    /**
     * 设置表格边框
     */
    private void setTableBorders(XWPFTable table) {
        try {
            CTTblPr tblPr = table.getCTTbl().getTblPr();
            if (tblPr == null) {
                tblPr = table.getCTTbl().addNewTblPr();
            }

            CTTblBorders borders = tblPr.getTblBorders();
            if (borders == null) {
                borders = tblPr.addNewTblBorders();
            }

            // 创建边框样式
            CTBorder border = CTBorder.Factory.newInstance();
            border.setVal(STBorder.SINGLE);
            border.setSz(BigInteger.valueOf(4));
            border.setColor("000000");

            // 设置所有边框
            if (borders.getTop() == null) borders.addNewTop().set(border);
            if (borders.getBottom() == null) borders.addNewBottom().set(border);
            if (borders.getLeft() == null) borders.addNewLeft().set(border);
            if (borders.getRight() == null) borders.addNewRight().set(border);
            if (borders.getInsideH() == null) borders.addNewInsideH().set(border);
            if (borders.getInsideV() == null) borders.addNewInsideV().set(border);

        } catch (Exception e) {
            log.warn("表格边框设置失败: {}", e.getMessage());
            // 使用简单的边框设置作为备选方案
            try {
                table.setTableAlignment(TableRowAlign.CENTER);
            } catch (Exception ex) {
                log.warn("简单边框设置也失败，使用默认样式");
            }
        }
    }

    /**
     * 生成表格预览HTML
     */
    public String generateTablePreview(TableExportRequest request) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>表格预览</title>");
        html.append("<style>");
        html.append("body { font-family: '宋体', SimSun, serif; margin: 20px; }");
        html.append("h1 { text-align: center; font-size: 18px; margin-bottom: 20px; }");
        html.append("table { border-collapse: collapse; width: 100%; margin: 0 auto; }");
        html.append("th, td { border: 1px solid #000; padding: 8px; text-align: center; vertical-align: middle; }");
        html.append("th { background-color: #f0f0f0; font-weight: bold; }");
        html.append(".merged-cell { background-color: #e6f3ff; }");
        html.append("</style>");
        html.append("</head><body>");

        // 添加标题
        if (request.getTitle() != null && !request.getTitle().trim().isEmpty()) {
            html.append("<h1>").append(request.getTitle()).append("</h1>");
        }

        // 创建表格
        html.append("<table>");

        TableExportRequest.TableData tableData = request.getTableData();
        List<TableExportRequest.MergeCell> merges = request.getMerges();

        // 处理表头
        if (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
            for (List<TableExportRequest.HeaderCell> headerRow : tableData.getHeaders()) {
                html.append("<tr>");
                for (TableExportRequest.HeaderCell headerCell : headerRow) {
                    html.append("<th>").append(headerCell.getContent() != null ? headerCell.getContent() : "").append("</th>");
                }
                html.append("</tr>");
            }
        }

        // 处理数据行
        if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
            for (int rowIndex = 0; rowIndex < tableData.getDataRows().size(); rowIndex++) {
                List<TableExportRequest.DataCell> dataRow = tableData.getDataRows().get(rowIndex);
                html.append("<tr>");
                for (int colIndex = 0; colIndex < dataRow.size(); colIndex++) {
                    TableExportRequest.DataCell dataCell = dataRow.get(colIndex);
                    boolean isMerged = isMergedMainCell(merges, rowIndex, colIndex);

                    String cellClass = isMerged ? " class='merged-cell'" : "";
                    String content = isMerged ? getMergedCellContent(merges, rowIndex, colIndex) : dataCell.getContent();

                    html.append("<td").append(cellClass).append(">")
                            .append(content != null ? content : "")
                            .append("</td>");
                }
                html.append("</tr>");
            }
        }

        html.append("</table>");
        html.append("</body></html>");

        return html.toString();
    }

    /**
     * 设置表格样式
     */
    private void setTableStyle(XWPFTable table) {
        // 设置表格边框
        CTTblBorders borders = CTTblBorders.Factory.newInstance();

        // 设置上边框
        CTBorder topBorder = borders.addNewTop();
        topBorder.setVal(STBorder.SINGLE);
        topBorder.setSz(BigInteger.valueOf(4));
        topBorder.setColor("000000");

        // 设置下边框
        CTBorder bottomBorder = borders.addNewBottom();
        bottomBorder.setVal(STBorder.SINGLE);
        bottomBorder.setSz(BigInteger.valueOf(4));
        bottomBorder.setColor("000000");

        // 设置左边框
        CTBorder leftBorder = borders.addNewLeft();
        leftBorder.setVal(STBorder.SINGLE);
        leftBorder.setSz(BigInteger.valueOf(4));
        leftBorder.setColor("000000");

        // 设置右边框
        CTBorder rightBorder = borders.addNewRight();
        rightBorder.setVal(STBorder.SINGLE);
        rightBorder.setSz(BigInteger.valueOf(4));
        rightBorder.setColor("000000");

        // 设置内部水平边框
        CTBorder insideHBorder = borders.addNewInsideH();
        insideHBorder.setVal(STBorder.SINGLE);
        insideHBorder.setSz(BigInteger.valueOf(4));
        insideHBorder.setColor("000000");

        // 设置内部垂直边框
        CTBorder insideVBorder = borders.addNewInsideV();
        insideVBorder.setVal(STBorder.SINGLE);
        insideVBorder.setSz(BigInteger.valueOf(4));
        insideVBorder.setColor("000000");

        // 应用边框到表格
        table.getCTTbl().getTblPr().setTblBorders(borders);

        // 设置表格对齐方式为居中
        CTJcTable jcTable = CTJcTable.Factory.newInstance();
        jcTable.setVal(STJcTable.CENTER);
        table.getCTTbl().getTblPr().setJc(jcTable);

        // 设置表格宽度为绝对值（与列宽保持一致）
        // 注意：这里暂时使用默认宽度，实际宽度会在设置列宽时动态计算
        CTTblWidth tblWidth = CTTblWidth.Factory.newInstance();
        tblWidth.setType(STTblWidth.DXA);
        tblWidth.setW(BigInteger.valueOf(12000)); // 默认宽度，会被动态调整
        table.getCTTbl().getTblPr().setTblW(tblWidth);

        log.debug("表格样式设置完成");
    }


    /**
     * 设置单元格内容（完整版本，支持公式、混合内容和MathML）
     */
    private void setCellContent(XWPFTableCell cell, String content, boolean hasMath, String mathML, Map<String, String> mathMLMap) {
        // 清除单元格现有内容
        cell.removeParagraph(0);

        if (content == null || content.trim().isEmpty()) {
            // 创建空段落
            XWPFParagraph paragraph = cell.addParagraph();
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            return;
        }

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        try {
            if (mathMLMap != null && !mathMLMap.isEmpty()) {
                // 处理混合内容（文本 + 公式）
                log.debug("处理混合内容，mathMLMap大小: {}", mathMLMap.size());
                insertMixedContent(paragraph, content, mathMLMap);
            } else if (mathML != null && !mathML.trim().isEmpty()) {
                // 处理纯公式内容
                log.debug("处理纯公式内容");
                insertMathFormula(paragraph, mathML);
            } else if (hasMath) {
                // 处理LaTeX格式的数学公式（兼容旧版本）
                processMathContent(cell, content);
            } else {
                // 处理普通文本内容
                insertTextWithLineBreaks(paragraph, content);
            }
        } catch (Exception e) {
            log.error("设置单元格内容失败: {}", e.getMessage(), e);
            // 如果处理失败，回退到普通文本
            paragraph.getRuns().clear();
            insertTextWithLineBreaks(paragraph, content);
        }
    }

    /**
     * 处理数学公式内容
     */
    private void processMathContent(XWPFTableCell cell, String content) {
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        try {
            // 检查是否为纯数学公式（被$包围）
            if (content.startsWith("$") && content.endsWith("$") && content.length() > 2) {
                String mathContent = content.substring(1, content.length() - 1);
                addMathFormula(paragraph, mathContent);
            } else {
                // 混合内容：文本 + 数学公式
                processMixedContent(paragraph, content);
            }
        } catch (Exception e) {
            log.warn("处理数学公式失败，使用普通文本: {}", content, e);
            // 如果数学公式处理失败，回退到普通文本
            processTextContent(cell, content);
        }
    }

    /**
     * 处理混合内容（文本 + 数学公式）
     */
    private void processMixedContent(XWPFParagraph paragraph, String content) {
        // 使用正则表达式分割文本和数学公式
        String[] parts = content.split("\\$");
        boolean isMath = false;

        for (String part : parts) {
            if (part.isEmpty()) {
                isMath = !isMath;
                continue;
            }

            if (isMath) {
                // 数学公式部分
                try {
                    addMathFormula(paragraph, part);
                } catch (Exception e) {
                    log.warn("处理数学公式部分失败: {}", part, e);
                    // 如果公式处理失败，作为普通文本处理
                    XWPFRun run = paragraph.createRun();
                    run.setText("$" + part + "$");
                    run.setFontFamily("宋体");
                    run.setFontSize(11);
                }
            } else {
                // 普通文本部分
                XWPFRun run = paragraph.createRun();
                run.setText(part);
                run.setFontFamily("宋体");
                run.setFontSize(11);
            }

            isMath = !isMath;
        }
    }

    /**
     * 处理普通文本内容
     */
    private void processTextContent(XWPFTableCell cell, String content) {
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 处理多行文本
        String[] lines = content.split("\\n");
        for (int i = 0; i < lines.length; i++) {
            XWPFRun run = paragraph.createRun();
            run.setText(lines[i]);
            run.setFontFamily("宋体");
            run.setFontSize(11);

            // 除了最后一行，其他行都添加换行符
            if (i < lines.length - 1) {
                run.addBreak();
            }
        }
    }


    /**
     * 设置单元格边框
     */
    private void setCellBorders(XWPFTableCell cell) {
        CTTcBorders borders = CTTcBorders.Factory.newInstance();

        // 设置上边框
        CTBorder topBorder = borders.addNewTop();
        topBorder.setVal(STBorder.SINGLE);
        topBorder.setSz(BigInteger.valueOf(4));
        topBorder.setColor("000000");

        // 设置下边框
        CTBorder bottomBorder = borders.addNewBottom();
        bottomBorder.setVal(STBorder.SINGLE);
        bottomBorder.setSz(BigInteger.valueOf(4));
        bottomBorder.setColor("000000");

        // 设置左边框
        CTBorder leftBorder = borders.addNewLeft();
        leftBorder.setVal(STBorder.SINGLE);
        leftBorder.setSz(BigInteger.valueOf(4));
        leftBorder.setColor("000000");

        // 设置右边框
        CTBorder rightBorder = borders.addNewRight();
        rightBorder.setVal(STBorder.SINGLE);
        rightBorder.setSz(BigInteger.valueOf(4));
        rightBorder.setColor("000000");

        // 应用边框到单元格
        cell.getCTTc().getTcPr().setTcBorders(borders);
    }

    /**
     * 添加数学公式到段落
     */
    private void addMathFormula(XWPFParagraph paragraph, String latexContent) {
        try {
            log.debug("添加数学公式: {}", latexContent);

            // 这里应该将LaTeX转换为MathML，然后转换为OMML
            // 由于没有LaTeX到MathML的转换器，我们先用简单的文本替代
            // 在实际项目中，可以集成MathJax或其他LaTeX转换库

            // 临时实现：将LaTeX公式作为特殊格式的文本显示
            XWPFRun run = paragraph.createRun();
            run.setText("[" + latexContent + "]");
            run.setFontFamily("Times New Roman");
            run.setFontSize(11);
            run.setItalic(true);

            log.debug("数学公式添加完成（临时文本格式）");

        } catch (Exception e) {
            log.error("添加数学公式失败: {}", latexContent, e);
            // 如果处理失败，添加错误提示
            XWPFRun run = paragraph.createRun();
            run.setText("[公式错误: " + latexContent + "]");
            run.setFontFamily("宋体");
            run.setFontSize(11);
            run.setColor("FF0000"); // 红色
        }
    }

    /**
     * 设置单元格宽度
     */
    private void setCellWidth(XWPFTableCell cell, Integer widthPx) {
        if (widthPx == null || widthPx <= 0) {
            return;
        }

        try {
            // 将像素转换为Twips (1像素 ≈ 15 Twips)
            int widthTwips = (int) (widthPx * 15);

            // 设置单元格宽度
            CTTcPr tcPr = cell.getCTTc().getTcPr();
            if (tcPr == null) {
                tcPr = cell.getCTTc().addNewTcPr();
            }

            CTTblWidth cellWidth = tcPr.getTcW();
            if (cellWidth == null) {
                cellWidth = tcPr.addNewTcW();
            }

            cellWidth.setType(STTblWidth.DXA);
            cellWidth.setW(BigInteger.valueOf(widthTwips));

            log.debug("设置单元格宽度: {}px ({}twips)", widthPx, widthTwips);
        } catch (Exception e) {
            log.warn("设置单元格宽度失败: {}", e.getMessage());
        }
    }

    /**
     * 设置单元格高度
     */
    private void setCellHeight(XWPFTableCell cell, Integer heightPx) {
        if (heightPx == null || heightPx <= 0) {
            return;
        }

        try {
            // 将像素转换为Twips (1像素 ≈ 15 Twips)
            int heightTwips = (int) (heightPx * 15);

            // 设置行高
            XWPFTableRow row = cell.getTableRow();
            CTTrPr trPr = row.getCtRow().getTrPr();
            if (trPr == null) {
                trPr = row.getCtRow().addNewTrPr();
            }

            // 设置行高
            CTHeight rowHeight = null;
            if (trPr.getTrHeightList().isEmpty()) {
                rowHeight = trPr.addNewTrHeight();
            } else {
                rowHeight = trPr.getTrHeightList().get(0);
            }

            rowHeight.setVal(BigInteger.valueOf(heightTwips));
            rowHeight.setHRule(STHeightRule.AT_LEAST);

            log.debug("设置单元格高度: {}px ({}twips)", heightPx, heightTwips);
        } catch (Exception e) {
            log.warn("设置单元格高度失败: {}", e.getMessage());
        }
    }

    /**
     * 导出新JSON格式的表格到Word文档
     * 处理示例JSON数据格式：
     * {
     * "cellRows": [[{"content": "智能手机\n（多功能检测）", "width": 180, "height": 50, "hasMath": false}, ...]],
     * "merges": [{"startRow": 2, "startCol": 0, "endRow": 3, "endCol": 0, "content": "智能手机\n（多功能检测）"}],
     * "headers": [["产品名称", "生产信息", "", "责任人员", "", ""], ["", "批次号", "日期", "检验员", "审核员", "负责人"]],
     * "headerMerges": [...],
     * "headerWidthConfig": {"columnWidths": [280, 100, 100, 80, 80, 80], "headerHeights": [60, 40]},
     * "metadata": {"totalRows": 2, "totalColumns": 6, "headerRows": 2, "title": "检验记录表"}
     * }
     */
    public byte[] exportNewJsonFormatToWord(JsonTableExportRequest jsonRequest) throws IOException {
        log.info("开始导出新JSON格式Word文档，表格标题: {}",
                jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : "未知标题");

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页面方向为横向（根据示例数据）
            setPageOrientation(document, "LANDSCAPE");

            // 添加标题
            /*String title = jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : jsonRequest.getTitle();
            if (title != null && !title.trim().isEmpty()) {
                addTitle(document, title);
            }*/

            // 创建表格
            createTableFromNewJsonFormat(document, jsonRequest);

            document.write(out);
            byte[] result = out.toByteArray();

            log.info("新JSON格式Word文档导出完成，文件大小: {} bytes", result.length);
            return result;
        }
    }

    /**
     * 从请求中获取表头数据
     */
    private List<List<String>> getHeadersFromRequest(JsonTableExportRequest jsonRequest) {
        // 优先从headerConfig中获取
        if (jsonRequest.getHeaderConfig() != null &&
                jsonRequest.getHeaderConfig().getHeaders() != null &&
                !jsonRequest.getHeaderConfig().getHeaders().isEmpty()) {
            log.debug("从headerConfig中获取表头数据");
            return jsonRequest.getHeaderConfig().getHeaders();
        }

        // 备用：从headers字段获取
        if (jsonRequest.getHeaders() != null && !jsonRequest.getHeaders().isEmpty()) {
            log.debug("从headers字段中获取表头数据");
            return jsonRequest.getHeaders();
        }

        log.warn("未找到表头数据");
        return null;
    }

    /**
     * 从请求中获取表头合并信息
     */
    private List<JsonTableExportRequest.MergeConfig> getHeaderMergesFromRequest(JsonTableExportRequest jsonRequest) {
        // 优先从headerConfig中获取
        if (jsonRequest.getHeaderConfig() != null &&
                jsonRequest.getHeaderConfig().getMerges() != null &&
                !jsonRequest.getHeaderConfig().getMerges().isEmpty()) {
            log.debug("从headerConfig中获取表头合并信息");
            return jsonRequest.getHeaderConfig().getMerges();
        }

        // 备用：从headerMerges字段获取
        if (jsonRequest.getHeaderMerges() != null && !jsonRequest.getHeaderMerges().isEmpty()) {
            log.debug("从headerMerges字段中获取表头合并信息");
            return jsonRequest.getHeaderMerges();
        }

        log.debug("未找到表头合并信息");
        return null;
    }

    /**
     * 从新JSON格式创建表格
     */
    private void createTableFromNewJsonFormat(XWPFDocument document, JsonTableExportRequest jsonRequest) {
        // 计算表格的总行数和列数
        // 优先从headerConfig中获取表头数据
        List<List<String>> headers = getHeadersFromRequest(jsonRequest);
        int headerRows = headers != null ? headers.size() : 0;
        int dataRows = jsonRequest.getCellRows() != null ? jsonRequest.getCellRows().size() : 0;
        int totalRows = headerRows + dataRows;

        int totalCols = 0;
        if (jsonRequest.getMetadata() != null && jsonRequest.getMetadata().getTotalColumns() != null) {
            totalCols = jsonRequest.getMetadata().getTotalColumns();
        } else if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
            totalCols = jsonRequest.getHeaderWidthConfig().getColumnWidths().size();
        } else if (headers != null && !headers.isEmpty()) {
            totalCols = headers.get(0).size();
        }

        log.info("创建新JSON格式表格，总行数: {}, 总列数: {}", totalRows, totalCols);

        // 创建表格，确保至少有1行1列
        if (totalRows <= 0) totalRows = 1;
        if (totalCols <= 0) totalCols = 1;

        XWPFTable table = document.createTable(totalRows, totalCols);

        // 验证表格创建成功
        if (table == null) {
            throw new RuntimeException("创建表格失败");
        }

        log.debug("表格创建成功，实际行数: {}, 实际列数: {}",
                table.getRows().size(),
                table.getRows().isEmpty() ? 0 : table.getRow(0).getTableCells().size());

        // 设置表格样式
        setTableStyleFromJsonConfig(table, jsonRequest);

        int currentRowIndex = 0;

        // 处理表头
        if (headers != null && !headers.isEmpty()) {
            log.info("开始处理表头，表头行数: {}", headers.size());
            for (int i = 0; i < headers.size(); i++) {
                List<String> headerRow = headers.get(i);
                log.debug("处理第{}行表头，列数: {}", i, headerRow != null ? headerRow.size() : 0);
                XWPFTableRow row = table.getRow(currentRowIndex);
                if (row == null) {
                    log.warn("表格第{}行为null，尝试创建新行", currentRowIndex);
                    row = table.createRow();
                }
                processHeaderRowFromJson(row, headerRow, jsonRequest, i);
                currentRowIndex++;
            }
            log.info("表头处理完成，当前行索引: {}", currentRowIndex);
        } else {
            log.warn("表头数据为空或null，跳过表头处理");
        }

        // 处理数据行
        if (jsonRequest.getCellRows() != null && !jsonRequest.getCellRows().isEmpty()) {
            for (List<JsonTableExportRequest.CellData> dataRow : jsonRequest.getCellRows()) {
                XWPFTableRow row = table.getRow(currentRowIndex);
                processDataRowFromJsonCellRows(row, dataRow, jsonRequest);
                currentRowIndex++;
            }
        }

        // 应用合并
        applyMergesFromJsonFormat(table, jsonRequest, headerRows);

        // 设置边框
        setTableBorders(table);
    }

    /**
     * 从JSON配置设置表格样式
     */
    private void setTableStyleFromJsonConfig(XWPFTable table, JsonTableExportRequest jsonRequest) {
        try {
            List<Integer> columnWidths = null;
            if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
                columnWidths = jsonRequest.getHeaderWidthConfig().getColumnWidths();
            }

            if (columnWidths != null && !columnWidths.isEmpty()) {
                // 计算总宽度
                int totalWidth = columnWidths.stream().mapToInt(Integer::intValue).sum();
                int totalWidthTwips = totalWidth * 15; // 像素转Twips

                // 设置表格宽度
                CTTblPr tblPr = table.getCTTbl().getTblPr();
                if (tblPr == null) {
                    tblPr = table.getCTTbl().addNewTblPr();
                }

                CTTblWidth tblWidth = tblPr.getTblW();
                if (tblWidth == null) {
                    tblWidth = tblPr.addNewTblW();
                }
                tblWidth.setType(STTblWidth.DXA);
                tblWidth.setW(BigInteger.valueOf(totalWidthTwips));

                // 设置表格居中
                CTJcTable jcTable = tblPr.getJc();
                if (jcTable == null) {
                    jcTable = tblPr.addNewJc();
                }
                jcTable.setVal(STJcTable.CENTER);

                log.info("设置表格总宽度: {}px ({}twips)", totalWidth, totalWidthTwips);
            }
        } catch (Exception e) {
            log.warn("设置表格样式失败: {}", e.getMessage());
        }
    }

    /**
     * 处理JSON格式的表头行
     */
    private void processHeaderRowFromJson(XWPFTableRow row, List<String> headerRow, JsonTableExportRequest jsonRequest, int rowIndex) {
        if (row == null) {
            log.error("表头行对象为null，无法处理");
            return;
        }

        if (headerRow == null || headerRow.isEmpty()) {
            log.warn("表头行数据为空，跳过处理");
            return;
        }

        log.debug("处理表头行，行索引: {}, 列数: {}", rowIndex, headerRow.size());

        for (int colIndex = 0; colIndex < headerRow.size(); colIndex++) {
            String content = headerRow.get(colIndex);
            XWPFTableCell cell = row.getCell(colIndex);

            if (cell == null) {
                log.debug("第{}列单元格为null，创建新单元格", colIndex);
                cell = row.createCell();
            }

            // 设置内容
            if (content != null && !content.trim().isEmpty()) {
                log.debug("设置表头单元格内容: [{}][{}] = {}", rowIndex, colIndex, content);
                // 检查是否需要纵向显示
                Boolean isVertical = false;
                if (jsonRequest.getVerticalHeadersConfig() != null && colIndex < jsonRequest.getVerticalHeadersConfig().size()) {
                    isVertical = jsonRequest.getVerticalHeadersConfig().get(colIndex);
                }
                setCellContentOptimized(cell, content, true, isVertical);
            } else {
                log.debug("表头单元格内容为空: [{}][{}]", rowIndex, colIndex);
            }

            // 设置列宽
            if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
                List<Integer> columnWidths = jsonRequest.getHeaderWidthConfig().getColumnWidths();
                if (colIndex < columnWidths.size() && columnWidths.get(colIndex) != null) {
                    setCellWidthOptimized(cell, columnWidths.get(colIndex));
                }
            }

            // 设置行高
            if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getHeaderHeights() != null) {
                List<Integer> headerHeights = jsonRequest.getHeaderWidthConfig().getHeaderHeights();
                if (rowIndex < headerHeights.size() && headerHeights.get(rowIndex) != null) {
                    setCellHeightOptimized(row, headerHeights.get(rowIndex));
                }
            }
        }
    }

    /**
     * 处理JSON格式的数据行（CellData格式）
     */
    private void processDataRowFromJsonCellRows(XWPFTableRow row, List<JsonTableExportRequest.CellData> dataRow, JsonTableExportRequest jsonRequest) {
        // 首先确定行高（使用所有单元格中的最大height值）
        Integer rowHeight = null;
        for (JsonTableExportRequest.CellData cellData : dataRow) {
            if (cellData.getHeight() != null && cellData.getHeight() > 0) {
                if (rowHeight == null || cellData.getHeight() > rowHeight) {
                    rowHeight = cellData.getHeight();
                }
            }
        }

        // 设置行高
        if (rowHeight != null) {
            setCellHeightOptimized(row, rowHeight);
            log.info("设置数据行高度: {}px ({}twips)", rowHeight, rowHeight * 15);
        } else {
            log.debug("数据行未设置高度，使用默认高度");
        }

        for (int colIndex = 0; colIndex < dataRow.size(); colIndex++) {
            JsonTableExportRequest.CellData cellData = dataRow.get(colIndex);
            XWPFTableCell cell = row.getCell(colIndex);

            if (cell == null) {
                cell = row.createCell();
            }

            // 检查是否有嵌套表格
            if (cellData.getNestedTable() != null && Boolean.TRUE.equals(cellData.getNestedTable().getEnabled())) {
                // 处理嵌套表格
                processNestedTableInCell(cell, cellData);
            } else {
                // 设置普通内容
                String content = cellData.getContent();
                if (content != null && !content.trim().isEmpty()) {
                    // 使用完整的setCellContent方法处理所有类型的内容（包括数学公式）
                    setCellContent(cell, content,
                            Boolean.TRUE.equals(cellData.getHasMath()),
                            cellData.getMathML(),
                            cellData.getMathMLMap());
                }
            }

            // 设置列宽（优先使用cellData中的width，然后使用headerWidthConfig）
            Integer cellWidth = cellData.getWidth();
            if (cellWidth != null && cellWidth > 0) {
                setCellWidthOptimized(cell, cellWidth);
            } else if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
                List<Integer> columnWidths = jsonRequest.getHeaderWidthConfig().getColumnWidths();
                if (colIndex < columnWidths.size() && columnWidths.get(colIndex) != null) {
                    setCellWidthOptimized(cell, columnWidths.get(colIndex));
                }
            }
        }
    }

    /**
     * 处理单元格中的嵌套表格
     */
    private void processNestedTableInCell(XWPFTableCell cell, JsonTableExportRequest.CellData cellData) {
        try {
            log.info("开始处理嵌套表格，父单元格内容: {}", cellData.getContent());

            JsonTableExportRequest.NestedTableConfig nestedTableConfig = cellData.getNestedTable();
            if (nestedTableConfig == null) {
                log.warn("嵌套表格配置对象为null，使用降级处理");
                fallbackToMainContent(cell, cellData);
                return;
            }

            JsonTableExportRequest.NestedTableData nestedData = nestedTableConfig.getConfig();
            if (nestedData == null) {
                log.warn("嵌套表格数据为null，使用降级处理");
                fallbackToMainContent(cell, cellData);
                return;
            }

            if (nestedData.getCellRows() == null || nestedData.getCellRows().isEmpty()) {
                log.warn("嵌套表格数据行为空，使用降级处理");
                fallbackToMainContent(cell, cellData);
                return;
            }

            // 清空单元格现有内容
            try {
                if (!cell.getParagraphs().isEmpty()) {
                    cell.removeParagraph(0);
                }
            } catch (Exception e) {
                log.warn("清空单元格内容失败，继续处理", e);
            }

            // 添加主内容（如果有）
            String mainContent = cellData.getContent();
            if (mainContent != null && !mainContent.trim().isEmpty()) {
                XWPFParagraph mainParagraph = cell.addParagraph();
                mainParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun mainRun = mainParagraph.createRun();
                mainRun.setText(mainContent);
                mainRun.setBold(true);
                mainRun.setFontSize(10);
                mainRun.setFontFamily("宋体");
            }

            // 创建嵌套表格
            createNestedTable(cell, nestedData);

            log.info("嵌套表格处理完成");

        } catch (Exception e) {
            log.error("处理嵌套表格失败", e);
            // 降级处理：只显示主内容
            fallbackToMainContent(cell, cellData);
        }
    }

    /**
     * 在单元格中创建嵌套表格
     */
    private void createNestedTable(XWPFTableCell parentCell, JsonTableExportRequest.NestedTableData nestedData) {
        if (nestedData.getCellRows() == null || nestedData.getCellRows().isEmpty()) {
            log.warn("嵌套表格数据为空，跳过创建");
            return;
        }

        // 计算嵌套表格的行列数
        int nestedRows = nestedData.getCellRows().size();
        int nestedCols = nestedData.getCellRows().get(0).size();

        log.info("创建嵌套表格: {}行 x {}列", nestedRows, nestedCols);

        // 创建真正的嵌套表格
        XWPFTable nestedTable = null;

        try {
            log.info("开始创建真正的嵌套表格");

            // 方法1：参考WordTableExample的实现方式，但需要确保表格正确初始化
            try {
                // 创建一个空段落作为间隔和插入点
                XWPFParagraph spaceParagraph = parentCell.addParagraph();

                // 在空段落位置插入嵌套表格
                nestedTable = parentCell.insertNewTbl(spaceParagraph.getCTP().newCursor());

                if (nestedTable != null) {
                    setNestedTableBorders(nestedTable);
                    // 创建嵌套表格的行（1行）
                    // 先创建一行，然后添加两个单元格

                    for (int i = 0; i < nestedRows; i++) {
                        XWPFTableRow nestedTableRow = nestedTable.createRow();

                        List<JsonTableExportRequest.CellData> rowData = nestedData.getCellRows().get(i);

                        for (int i1 = 0; i1 < nestedCols; i1++) {
                            XWPFTableCell nestedCell = nestedTableRow.createCell();

                            JsonTableExportRequest.CellData cellData = rowData.get(i1);

                            // 清除默认内容
                            nestedCell.removeParagraph(0);

                            // 添加新段落并设置内容
                            XWPFParagraph nestedPara = nestedCell.addParagraph();
                            nestedPara.setAlignment(ParagraphAlignment.CENTER);
                            nestedPara.createRun().setText(cellData.getContent());

                            // 设置嵌套单元格垂直居中
                            nestedCell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                        }

                    }


                    log.info("使用WordTableExample方式创建嵌套表格成功");
                } else {
                    throw new RuntimeException("insertNewTbl返回null");
                }

            } catch (Exception e) {
                log.warn("使用WordTableExample方式创建嵌套表格失败: {}", e.getMessage());
            }

            if (nestedTable == null) {
                throw new RuntimeException("无法创建嵌套表格");
            }

        } catch (Exception e) {
            log.error("创建嵌套表格失败", e);
            return;
        }

        /*// 如果嵌套表格创建成功，继续处理
        try {

            // 填充嵌套表格数据
            fillNestedTableData(nestedTable, nestedData);

            // 设置嵌套表格样式
            setNestedTableStyle(nestedTable, nestedData);

            log.info("嵌套表格创建和配置完成");

        } catch (Exception e) {
            log.error("配置嵌套表格时发生异常", e);
        }*/
    }

    // 设置表格边框的辅助方法
    private void setNestedTableBorders(XWPFTable table) {
        if (table.getCTTbl().getTblPr() == null) {
            table.getCTTbl().addNewTblPr();
        }
        if (table.getCTTbl().getTblPr().getTblBorders() == null) {
            table.getCTTbl().getTblPr().addNewTblBorders();
        }

        table.getCTTbl().getTblPr().getTblBorders().addNewLeft().setVal(STBorder.SINGLE);
        table.getCTTbl().getTblPr().getTblBorders().addNewRight().setVal(STBorder.SINGLE);
        table.getCTTbl().getTblPr().getTblBorders().addNewTop().setVal(STBorder.SINGLE);
        table.getCTTbl().getTblPr().getTblBorders().addNewBottom().setVal(STBorder.SINGLE);
        table.getCTTbl().getTblPr().getTblBorders().addNewInsideH().setVal(STBorder.SINGLE);
        table.getCTTbl().getTblPr().getTblBorders().addNewInsideV().setVal(STBorder.SINGLE);
    }

    /**
     * 填充嵌套表格数据
     */
    private void fillNestedTableData(XWPFTable nestedTable, JsonTableExportRequest.NestedTableData nestedData) {
        if (nestedTable == null) {
            log.error("嵌套表格对象为null，无法填充数据");
            return;
        }

        if (nestedData == null || nestedData.getCellRows() == null) {
            log.warn("嵌套表格数据为null，跳过填充");
            return;
        }

        List<List<JsonTableExportRequest.CellData>> cellRows = nestedData.getCellRows();

        for (int rowIndex = 0; rowIndex < cellRows.size(); rowIndex++) {
            List<JsonTableExportRequest.CellData> rowData = cellRows.get(rowIndex);
            if (rowData == null) {
                log.warn("嵌套表格第{}行数据为null，跳过", rowIndex);
                continue;
            }

            XWPFTableRow tableRow = nestedTable.getRow(rowIndex);
            if (tableRow == null) {
                log.warn("嵌套表格第{}行对象为null，跳过", rowIndex);
                continue;
            }

            for (int colIndex = 0; colIndex < rowData.size(); colIndex++) {
                JsonTableExportRequest.CellData cellData = rowData.get(colIndex);
                if (cellData == null) {
                    log.debug("嵌套表格单元格[{}][{}]数据为null，跳过", rowIndex, colIndex);
                    continue;
                }

                XWPFTableCell tableCell = tableRow.getCell(colIndex);
                if (tableCell == null) {
                    log.warn("嵌套表格单元格[{}][{}]对象为null，跳过", rowIndex, colIndex);
                    continue;
                }

                // 设置单元格内容
                String content = cellData.getContent();
                if (content != null && !content.trim().isEmpty()) {
                    tableCell.removeParagraph(0);
                    XWPFParagraph paragraph = tableCell.addParagraph();
                    paragraph.setAlignment(ParagraphAlignment.CENTER);
                    XWPFRun run = paragraph.createRun();
                    run.setText(content);
                    run.setFontSize(8);
                    run.setFontFamily("宋体");

                    // 处理数学公式（如果有）
                    if (Boolean.TRUE.equals(cellData.getHasMath())) {
                        // 简化的数学公式处理
                        run.setBold(true);
                    }
                }

                // 设置单元格尺寸
                if (cellData.getWidth() != null && cellData.getWidth() > 0) {
                    setCellWidthOptimized(tableCell, cellData.getWidth());
                }
            }
        }
    }

    /**
     * 设置嵌套表格样式
     */
    private void setNestedTableStyle(XWPFTable nestedTable, JsonTableExportRequest.NestedTableData nestedData) {
        // 设置表格边框
        setTableBorders(nestedTable);

        // 设置列宽（如果有配置）
        if (nestedData.getHeaderWidthConfig() != null &&
                nestedData.getHeaderWidthConfig().getColumnWidths() != null) {

            List<Integer> columnWidths = nestedData.getHeaderWidthConfig().getColumnWidths();
            for (int colIndex = 0; colIndex < columnWidths.size(); colIndex++) {
                Integer width = columnWidths.get(colIndex);
                if (width != null && width > 0) {
                    setNestedTableColumnWidth(nestedTable, colIndex, width);
                }
            }
        }
    }

    /**
     * 设置嵌套表格列宽
     */
    private void setNestedTableColumnWidth(XWPFTable nestedTable, int colIndex, int width) {
        for (XWPFTableRow row : nestedTable.getRows()) {
            if (colIndex < row.getTableCells().size()) {
                XWPFTableCell cell = row.getCell(colIndex);
                setCellWidthOptimized(cell, width);
            }
        }
    }


    /**
     * 应用单个嵌套表格合并
     */
    private void applyNestedTableMerge(XWPFTable nestedTable, JsonTableExportRequest.MergeConfig merge) {
        int startRow = merge.getStartRow();
        int startCol = merge.getStartCol();
        int endRow = merge.getEndRow();
        int endCol = merge.getEndCol();

        // 验证合并范围
        if (startRow >= nestedTable.getRows().size() || endRow >= nestedTable.getRows().size() ||
                startCol >= nestedTable.getRow(startRow).getTableCells().size() ||
                endCol >= nestedTable.getRow(startRow).getTableCells().size()) {
            log.warn("嵌套表格合并范围超出表格边界，跳过: {}", merge);
            return;
        }

        // 应用水平合并
        if (startCol < endCol) {
            applyHorizontalMerge(nestedTable, startRow, startCol, endCol);
        }

        // 应用垂直合并
        if (startRow < endRow) {
            applyVerticalMerge(nestedTable, startRow, endRow, startCol);
        }

        // 设置合并单元格内容
        if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
            XWPFTableCell mainCell = nestedTable.getRow(startRow).getCell(startCol);
            mainCell.removeParagraph(0);
            XWPFParagraph paragraph = mainCell.addParagraph();
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun run = paragraph.createRun();
            run.setText(merge.getContent());
            run.setFontSize(8);
            run.setFontFamily("宋体");
        }
    }

    /**
     * 安全创建嵌套表格的方法
     */
    private XWPFTable createSafeNestedTable(XWPFTableCell parentCell, int rows, int cols) {
        try {
            log.info("使用安全方式创建嵌套表格: {}行 x {}列", rows, cols);

            // 创建一个空段落作为插入点
            XWPFParagraph insertParagraph = parentCell.addParagraph();

            // 使用insertNewTbl方法，但立即进行完整初始化
            XWPFTable nestedTable = parentCell.insertNewTbl(insertParagraph.getCTP().newCursor());

            if (nestedTable == null) {
                throw new RuntimeException("insertNewTbl返回null");
            }

            // 立即创建完整的表格结构，避免后续的XML连接问题
            // 先确保有第一行
            if (nestedTable.getRows().isEmpty()) {
                nestedTable.createRow();
            }

            // 创建所有需要的行
            while (nestedTable.getRows().size() < rows) {
                nestedTable.createRow();
            }

            // 为每行创建所有需要的列
            for (int i = 0; i < rows; i++) {
                XWPFTableRow row = nestedTable.getRow(i);
                if (row != null) {
                    // 确保第一个单元格存在
                    if (row.getTableCells().isEmpty()) {
                        row.createCell();
                    }
                    // 创建所有需要的单元格
                    while (row.getTableCells().size() < cols) {
                        row.createCell();
                    }
                }
            }

            log.info("安全方式创建嵌套表格成功，结构: {}行 x {}列",
                    nestedTable.getRows().size(),
                    nestedTable.getRows().isEmpty() ? 0 : nestedTable.getRow(0).getTableCells().size());

            return nestedTable;

        } catch (Exception e) {
            log.error("安全方式创建嵌套表格失败", e);
            return null;
        }
    }

    /**
     * 直接创建嵌套表格的方法
     */
    private XWPFTable createNestedTableDirectly(XWPFTableCell parentCell, int rows, int cols) {
        try {
            log.info("使用简化方式创建嵌套表格: {}行 x {}列", rows, cols);

            // 简化方案：创建一个临时文档，在其中创建表格，然后复制XML
            XWPFDocument tempDoc = new XWPFDocument();
            XWPFTable tempTable = tempDoc.createTable(rows, cols);

            // 获取临时表格的XML
            org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTbl ctTbl = tempTable.getCTTbl();

            // 创建新的表格对象
            XWPFTable nestedTable = new XWPFTable(ctTbl, parentCell.getTableRow().getTable().getBody());

            // 将表格XML插入到单元格中
            XWPFParagraph paragraph = parentCell.addParagraph();
            XmlCursor cursor = paragraph.getCTP().newCursor();
            cursor.toEndToken();

            // 复制表格XML到光标位置
            XmlCursor tableCursor = ctTbl.newCursor();
            tableCursor.copyXml(cursor);

            // 清理临时文档
            tempDoc.close();

            log.info("简化方式创建嵌套表格成功");
            return nestedTable;

        } catch (Exception e) {
            log.error("简化方式创建嵌套表格失败", e);
            return null;
        }
    }

    /**
     * 创建嵌套表格的文本替代方案
     */
    private void createNestedTableAsText(XWPFTableCell parentCell, JsonTableExportRequest.NestedTableData nestedData, int rows, int cols) {
        try {
            log.info("使用文本方式表示嵌套表格: {}行 x {}列", rows, cols);

            // 添加嵌套表格标题
            XWPFParagraph titleParagraph = parentCell.addParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.LEFT);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("【详细信息】");
            titleRun.setBold(true);
            titleRun.setFontSize(9);
            titleRun.setFontFamily("宋体");
            titleRun.setColor("333333");

            // 添加嵌套表格内容
            for (int rowIndex = 0; rowIndex < rows; rowIndex++) {
                List<JsonTableExportRequest.CellData> rowData = nestedData.getCellRows().get(rowIndex);

                XWPFParagraph rowParagraph = parentCell.addParagraph();
                rowParagraph.setAlignment(ParagraphAlignment.LEFT);

                StringBuilder rowText = new StringBuilder();
                for (int colIndex = 0; colIndex < rowData.size(); colIndex++) {
                    JsonTableExportRequest.CellData cellData = rowData.get(colIndex);
                    if (cellData != null && cellData.getContent() != null) {
                        if (colIndex > 0) {
                            rowText.append(" | ");
                        }
                        rowText.append(cellData.getContent());
                    }
                }

                XWPFRun rowRun = rowParagraph.createRun();
                rowRun.setText(rowText.toString());
                rowRun.setFontSize(8);
                rowRun.setFontFamily("宋体");
                rowRun.setColor("666666");
            }

            log.info("嵌套表格文本内容添加完成");

        } catch (Exception e) {
            log.error("创建嵌套表格文本替代方案失败", e);
        }
    }

    /**
     * 降级处理：只显示主内容
     */
    private void fallbackToMainContent(XWPFTableCell cell, JsonTableExportRequest.CellData cellData) {
        try {
            // 清空现有内容
            try {
                if (!cell.getParagraphs().isEmpty()) {
                    cell.removeParagraph(0);
                }
            } catch (Exception e) {
                log.debug("清空单元格内容时出现异常，继续处理", e);
            }

            String content = cellData.getContent();
            if (content != null && !content.trim().isEmpty()) {
                XWPFParagraph paragraph = cell.addParagraph();
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun run = paragraph.createRun();
                run.setText(content);
                run.setFontSize(10);
                run.setFontFamily("宋体");

                // 添加嵌套表格提示
                if (cellData.getNestedTable() != null && Boolean.TRUE.equals(cellData.getNestedTable().getEnabled())) {
                    run.addBreak();
                    XWPFRun hintRun = paragraph.createRun();
                    hintRun.setText("(包含嵌套表格)");
                    hintRun.setFontSize(8);
                    hintRun.setColor("999999");
                }
            }
        } catch (Exception e) {
            log.error("降级处理也失败", e);
        }
    }

    /**
     * 应用JSON格式的合并
     */
    private void applyMergesFromJsonFormat(XWPFTable table, JsonTableExportRequest jsonRequest, int headerRows) {
        // 获取表头合并信息
        List<JsonTableExportRequest.MergeConfig> headerMerges = getHeaderMergesFromRequest(jsonRequest);

        // 应用表头合并
        if (headerMerges != null && !headerMerges.isEmpty()) {
            log.info("应用JSON格式表头合并单元格，数量: {}", headerMerges.size());
            for (JsonTableExportRequest.MergeConfig merge : headerMerges) {
                applyJsonHeaderMerge(table, merge); // 表头合并使用专门的方法
            }
        } else {
            log.debug("没有表头合并信息");
        }

        // 应用数据行合并
        if (jsonRequest.getMerges() != null && !jsonRequest.getMerges().isEmpty()) {
            log.info("应用JSON格式数据行合并单元格，数量: {}，表头偏移: {}", jsonRequest.getMerges().size(), headerRows);
            for (JsonTableExportRequest.MergeConfig merge : jsonRequest.getMerges()) {
                applyJsonDataMerge(table, merge, headerRows); // 数据行合并使用专门的方法
            }
        }
    }

    /**
     * 应用JSON格式的表头合并
     */
    private void applyJsonHeaderMerge(XWPFTable table, JsonTableExportRequest.MergeConfig merge) {
        // 验证合并信息的有效性
        if (!isValidJsonHeaderMerge(table, merge)) {
            log.warn("跳过无效的JSON表头合并信息: {}", merge);
            return;
        }

        // 表头合并直接使用原始索引，不需要偏移
        int actualStartRow = merge.getStartRow();
        int actualEndRow = merge.getEndRow();

        // 获取主单元格
        XWPFTableCell mainCell = table.getRow(actualStartRow).getCell(merge.getStartCol());

        // 设置主单元格内容
        if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
            setCellContentOptimized(mainCell, merge.getContent(), true, false); // 表头单元格
        }

        // 计算跨度
        int colspan = merge.getEndCol() - merge.getStartCol() + 1;
        int rowspan = merge.getEndRow() - merge.getStartRow() + 1;

        log.info("应用表头合并: 行[{}-{}], 列[{}-{}], 跨行: {}, 跨列: {}, 内容: '{}'",
                actualStartRow, actualEndRow, merge.getStartCol(), merge.getEndCol(),
                rowspan, colspan, merge.getContent());

        // 水平合并
        if (colspan > 1) {
            applyHorizontalMerge(table, actualStartRow, merge.getStartCol(), merge.getEndCol());
        }

        // 垂直合并
        if (rowspan > 1) {
            applyVerticalMerge(table, actualStartRow, actualEndRow, merge.getStartCol());
        }
    }

    /**
     * 应用JSON格式的数据行合并
     */
    private void applyJsonDataMerge(XWPFTable table, JsonTableExportRequest.MergeConfig merge, int headerRowCount) {
        // 验证合并信息的有效性
        if (!isValidJsonDataMerge(table, merge, headerRowCount)) {
            log.warn("跳过无效的JSON数据行合并信息: {}", merge);
            return;
        }

        // 数据行合并需要加上表头偏移
        int actualStartRow = merge.getStartRow();
        int actualEndRow = merge.getEndRow();

        // 检查索引是否已经包含表头偏移
        // 如果startRow >= headerRowCount，说明是绝对索引，不需要再加偏移
        // 如果startRow < headerRowCount，说明是相对索引，需要加偏移
        if (actualStartRow < headerRowCount) {
            // 相对索引，需要加偏移
            actualStartRow += headerRowCount;
            actualEndRow += headerRowCount;
            log.info("数据行合并使用相对索引，加上表头偏移: {} -> {}", merge.getStartRow(), actualStartRow);
        } else {
            // 绝对索引，不需要偏移
            log.info("数据行合并使用绝对索引，不需要偏移: {}", actualStartRow);
        }

        // 获取主单元格
        XWPFTableCell mainCell = table.getRow(actualStartRow).getCell(merge.getStartCol());

        // 设置主单元格内容
        if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
            setCellContentOptimized(mainCell, merge.getContent(), false, false); // 数据单元格
        }

        // 计算跨度
        int colspan = merge.getEndCol() - merge.getStartCol() + 1;
        int rowspan = actualEndRow - actualStartRow + 1;

        log.info("应用数据行合并: 行[{}-{}], 列[{}-{}], 跨行: {}, 跨列: {}, 内容: '{}'",
                actualStartRow, actualEndRow, merge.getStartCol(), merge.getEndCol(),
                rowspan, colspan, merge.getContent());

        // 水平合并
        if (colspan > 1) {
            applyHorizontalMerge(table, actualStartRow, merge.getStartCol(), merge.getEndCol());
        }

        // 垂直合并
        if (rowspan > 1) {
            applyVerticalMerge(table, actualStartRow, actualEndRow, merge.getStartCol());
        }
    }

    /**
     * 验证JSON表头合并信息是否有效
     */
    private boolean isValidJsonHeaderMerge(XWPFTable table, JsonTableExportRequest.MergeConfig merge) {
        if (merge == null) {
            log.warn("JSON表头合并信息为null");
            return false;
        }

        int tableRows = table.getRows().size();
        int tableCols = table.getRow(0).getTableCells().size();

        // 检查行索引
        if (merge.getStartRow() < 0 || merge.getStartRow() >= tableRows) {
            log.warn("JSON表头合并起始行索引超出范围: {} (表格行数: {})", merge.getStartRow(), tableRows);
            return false;
        }
        if (merge.getEndRow() < 0 || merge.getEndRow() >= tableRows) {
            log.warn("JSON表头合并结束行索引超出范围: {} (表格行数: {})", merge.getEndRow(), tableRows);
            return false;
        }

        // 检查列索引
        if (merge.getStartCol() < 0 || merge.getStartCol() >= tableCols) {
            log.warn("JSON表头合并起始列索引超出范围: {} (表格列数: {})", merge.getStartCol(), tableCols);
            return false;
        }
        if (merge.getEndCol() < 0 || merge.getEndCol() >= tableCols) {
            log.warn("JSON表头合并结束列索引超出范围: {} (表格列数: {})", merge.getEndCol(), tableCols);
            return false;
        }

        // 检查合并范围的逻辑性
        if (merge.getStartRow() > merge.getEndRow()) {
            log.warn("JSON表头合并起始行大于结束行: {} > {}", merge.getStartRow(), merge.getEndRow());
            return false;
        }
        if (merge.getStartCol() > merge.getEndCol()) {
            log.warn("JSON表头合并起始列大于结束列: {} > {}", merge.getStartCol(), merge.getEndCol());
            return false;
        }

        return true;
    }

    /**
     * 验证JSON数据行合并信息是否有效
     */
    private boolean isValidJsonDataMerge(XWPFTable table, JsonTableExportRequest.MergeConfig merge, int headerRowCount) {
        if (merge == null) {
            log.warn("JSON数据行合并信息为null");
            return false;
        }

        int tableRows = table.getRows().size();
        int tableCols = table.getRow(0).getTableCells().size();

        // 计算实际行索引
        int actualStartRow = merge.getStartRow();
        int actualEndRow = merge.getEndRow();

        // 如果索引小于表头行数，说明是相对索引，需要加偏移
        if (actualStartRow < headerRowCount) {
            actualStartRow += headerRowCount;
            actualEndRow += headerRowCount;
        }

        // 检查行索引
        if (actualStartRow < 0 || actualStartRow >= tableRows) {
            log.warn("JSON数据行合并起始行索引超出范围: {} (表格行数: {})", actualStartRow, tableRows);
            return false;
        }
        if (actualEndRow < 0 || actualEndRow >= tableRows) {
            log.warn("JSON数据行合并结束行索引超出范围: {} (表格行数: {})", actualEndRow, tableRows);
            return false;
        }

        // 检查列索引
        if (merge.getStartCol() < 0 || merge.getStartCol() >= tableCols) {
            log.warn("JSON数据行合并起始列索引超出范围: {} (表格列数: {})", merge.getStartCol(), tableCols);
            return false;
        }
        if (merge.getEndCol() < 0 || merge.getEndCol() >= tableCols) {
            log.warn("JSON数据行合并结束列索引超出范围: {} (表格列数: {})", merge.getEndCol(), tableCols);
            return false;
        }

        // 检查合并范围的逻辑性
        if (actualStartRow > actualEndRow) {
            log.warn("JSON数据行合并起始行大于结束行: {} > {}", actualStartRow, actualEndRow);
            return false;
        }
        if (merge.getStartCol() > merge.getEndCol()) {
            log.warn("JSON数据行合并起始列大于结束列: {} > {}", merge.getStartCol(), merge.getEndCol());
            return false;
        }

        return true;
    }

    /**
     * 导出新JSON格式的表格到Word文档（支持分页符）
     */
    public byte[] exportNewJsonFormatToWordWithPageBreaks(JsonTableExportRequest jsonRequest) throws IOException {
        log.info("开始导出支持分页符的JSON格式Word文档，表格标题: {}",
                jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : "未知标题");

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页面方向为横向
            setPageOrientation(document, "LANDSCAPE");

            // 添加标题
            String title = jsonRequest.getTitle();
            if (title != null && !title.trim().isEmpty()) {
                addTitle(document, title);
            }

            // 创建表格（支持分页符处理）
            createTableFromNewJsonFormatWithPageBreaks(document, jsonRequest);

            document.write(out);
            byte[] result = out.toByteArray();

            log.info("支持分页符的JSON格式Word文档导出完成，文件大小: {} bytes", result.length);
            return result;
        }
    }

    /**
     * 从新JSON格式创建表格（支持分页符处理）
     */
    private void createTableFromNewJsonFormatWithPageBreaks(XWPFDocument document, JsonTableExportRequest jsonRequest) {
        // 计算表格的总行数和列数
        int headerRows = jsonRequest.getHeaders() != null ? jsonRequest.getHeaders().size() : 0;
        int totalCols = 8; // 固定8列

        log.info("创建支持分页符的JSON格式表格，表头行数: {}, 总列数: {}", headerRows, totalCols);

        // 处理数据行，按分页符分割
        if (jsonRequest.getCellRows() != null && !jsonRequest.getCellRows().isEmpty()) {
            List<List<List<JsonTableExportRequest.CellData>>> pages = splitRowsByPageBreaks(jsonRequest.getCellRows());

            for (int pageIndex = 0; pageIndex < pages.size(); pageIndex++) {
                List<List<JsonTableExportRequest.CellData>> pageRows = pages.get(pageIndex);

                // 为每页创建一个表格
                int totalRowsForPage = headerRows + pageRows.size();
                XWPFTable table = document.createTable(totalRowsForPage, totalCols);

                // 设置表格样式
                setTableStyleFromJsonConfig(table, jsonRequest);

                int currentRowIndex = 0;

                // 处理表头（每页都有表头）
                if (jsonRequest.getHeaders() != null && !jsonRequest.getHeaders().isEmpty()) {
                    for (int i = 0; i < jsonRequest.getHeaders().size(); i++) {
                        List<String> headerRow = jsonRequest.getHeaders().get(i);
                        XWPFTableRow row = table.getRow(currentRowIndex);
                        processHeaderRowFromJson(row, headerRow, jsonRequest, i);
                        currentRowIndex++;
                    }
                }

                // 处理当前页的数据行
                for (List<JsonTableExportRequest.CellData> dataRow : pageRows) {
                    XWPFTableRow row = table.getRow(currentRowIndex);
                    processDataRowFromJsonCellRows(row, dataRow, jsonRequest);
                    currentRowIndex++;
                }

                // 应用合并（仅对当前页）
                applyMergesFromJsonFormat(table, jsonRequest, headerRows);

                // 设置边框
                setTableBorders(table);

                log.info("第{}页表格创建完成，数据行数: {}", pageIndex + 1, pageRows.size());

                // 如果不是最后一页，添加分页符
                if (pageIndex < pages.size() - 1) {
                    addPageBreak(document);
                }
            }
        }
    }

    /**
     * 按分页符分割数据行
     */
    private List<List<List<JsonTableExportRequest.CellData>>> splitRowsByPageBreaks(List<List<JsonTableExportRequest.CellData>> allRows) {
        List<List<List<JsonTableExportRequest.CellData>>> pages = new ArrayList<>();
        List<List<JsonTableExportRequest.CellData>> currentPage = new ArrayList<>();

        for (List<JsonTableExportRequest.CellData> row : allRows) {
            // 检查是否是分页符行
            if (isPageBreakRow(row)) {
                // 保存当前页并开始新页
                if (!currentPage.isEmpty()) {
                    pages.add(new ArrayList<>(currentPage));
                    currentPage.clear();
                }
                log.debug("检测到分页符，开始新页");
            } else {
                // 普通数据行，添加到当前页
                currentPage.add(row);
            }
        }

        // 添加最后一页
        if (!currentPage.isEmpty()) {
            pages.add(currentPage);
        }

        log.info("数据行按分页符分割完成，共{}页", pages.size());
        return pages;
    }

    /**
     * 检查是否是分页符行
     */
    private boolean isPageBreakRow(List<JsonTableExportRequest.CellData> row) {
        if (row == null || row.isEmpty()) {
            return false;
        }

        JsonTableExportRequest.CellData firstCell = row.get(0);
        return firstCell != null && "__PAGE_BREAK__".equals(firstCell.getContent());
    }

    /**
     * 添加分页符
     */
    private void addPageBreak(XWPFDocument document) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.addBreak(BreakType.PAGE);
        log.debug("添加分页符");
    }
}
